<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WhatsAppWebController extends Controller
{
    private string $gatewayUrl;

    public function __construct()
    {
        $this->gatewayUrl = config('whatsapp.gateway_url', 'http://localhost:3000');
    }

    /**
     * Display the WhatsApp dashboard
     */
    public function dashboard()
    {
        try {
            $response = Http::get("{$this->gatewayUrl}/api/sessions");
            $sessions = $response->successful() ? $response->json()['sessions'] ?? [] : [];
            
            return view('whatsapp.dashboard', compact('sessions'));
        } catch (\Exception $e) {
            Log::error('Failed to fetch sessions for dashboard', ['error' => $e->getMessage()]);
            return view('whatsapp.dashboard', ['sessions' => []]);
        }
    }

    /**
     * Show session management page
     */
    public function sessions()
    {
        try {
            $response = Http::get("{$this->gatewayUrl}/api/sessions");
            $sessions = $response->successful() ? $response->json()['sessions'] ?? [] : [];
            
            return view('whatsapp.sessions', compact('sessions'));
        } catch (\Exception $e) {
            Log::error('Failed to fetch sessions', ['error' => $e->getMessage()]);
            return view('whatsapp.sessions', ['sessions' => []]);
        }
    }

    /**
     * Show specific session details
     */
    public function sessionDetail(string $sessionId)
    {
        try {
            $response = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}");
            
            if ($response->successful()) {
                $session = $response->json()['session'];
                return view('whatsapp.session-detail', compact('session', 'sessionId'));
            }
            
            return redirect()->route('whatsapp.sessions')->with('error', 'Session not found');
        } catch (\Exception $e) {
            Log::error('Failed to fetch session details', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('whatsapp.sessions')->with('error', 'Failed to load session details');
        }
    }

    /**
     * Show message sending interface
     */
    public function sendMessage(string $sessionId)
    {
        try {
            $response = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}");
            
            if ($response->successful()) {
                $session = $response->json()['session'];
                return view('whatsapp.send-message', compact('session', 'sessionId'));
            }
            
            return redirect()->route('whatsapp.sessions')->with('error', 'Session not found');
        } catch (\Exception $e) {
            Log::error('Failed to load send message page', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('whatsapp.sessions')->with('error', 'Failed to load send message page');
        }
    }

    /**
     * Show messages for a session
     */
    public function messages(string $sessionId)
    {
        try {
            $sessionResponse = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}");
            $messagesResponse = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}/messages");
            
            if ($sessionResponse->successful()) {
                $session = $sessionResponse->json()['session'];
                $messages = $messagesResponse->successful() ? $messagesResponse->json()['messages'] ?? [] : [];
                
                return view('whatsapp.messages', compact('session', 'sessionId', 'messages'));
            }
            
            return redirect()->route('whatsapp.sessions')->with('error', 'Session not found');
        } catch (\Exception $e) {
            Log::error('Failed to load messages page', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('whatsapp.sessions')->with('error', 'Failed to load messages');
        }
    }

    /**
     * Show contacts for a session
     */
    public function contacts(string $sessionId)
    {
        try {
            $sessionResponse = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}");
            $contactsResponse = Http::get("{$this->gatewayUrl}/api/sessions/{$sessionId}/contacts");
            
            if ($sessionResponse->successful()) {
                $session = $sessionResponse->json()['session'];
                $contacts = $contactsResponse->successful() ? $contactsResponse->json()['contacts'] ?? [] : [];
                
                return view('whatsapp.contacts', compact('session', 'sessionId', 'contacts'));
            }
            
            return redirect()->route('whatsapp.sessions')->with('error', 'Session not found');
        } catch (\Exception $e) {
            Log::error('Failed to load contacts page', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            
            return redirect()->route('whatsapp.sessions')->with('error', 'Failed to load contacts');
        }
    }
}