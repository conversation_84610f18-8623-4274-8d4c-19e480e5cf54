<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WhatsAppWebController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect()->route('whatsapp.dashboard');
});

/*
|--------------------------------------------------------------------------
| WhatsApp Web Interface Routes
|--------------------------------------------------------------------------
*/

Route::prefix('whatsapp')->name('whatsapp.')->group(function () {
    Route::get('/dashboard', [WhatsAppWebController::class, 'dashboard'])->name('dashboard');
    Route::get('/sessions', [WhatsAppWebController::class, 'sessions'])->name('sessions');
    Route::get('/sessions/{sessionId}', [WhatsAppWebController::class, 'sessionDetail'])->name('session.detail');
    Route::get('/sessions/{sessionId}/send', [WhatsAppWebController::class, 'sendMessage'])->name('send.message');
    Route::get('/sessions/{sessionId}/messages', [WhatsAppWebController::class, 'messages'])->name('messages');
    Route::get('/sessions/{sessionId}/contacts', [WhatsAppWebController::class, 'contacts'])->name('contacts');
});
