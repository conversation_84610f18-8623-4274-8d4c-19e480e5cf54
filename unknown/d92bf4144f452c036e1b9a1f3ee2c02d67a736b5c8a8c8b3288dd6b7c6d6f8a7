import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import { WhatsAppGateway } from './gateway/WhatsAppGateway';
import { setupRoutes } from './routes/api';
import { DatabaseManager } from './database/DatabaseManager';
import pino from 'pino';

// Load environment variables
dotenv.config({ path: '../.env' });

// Initialize logger
const logger = pino({ 
  level: 'info',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true
    }
  }
});

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.APP_URL || "http://localhost:8000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Initialize database
const dbManager = new DatabaseManager();

// Initialize WhatsApp Gateway
const whatsappGateway = new WhatsAppGateway(io, dbManager);

// Setup API routes
setupRoutes(app, whatsappGateway);

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);
  
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
  
  // Handle device management
  socket.on('create-session', async (data) => {
    try {
      const { sessionId } = data;
      await whatsappGateway.createSession(sessionId);
      socket.emit('session-created', { sessionId, success: true });
    } catch (error) {
      socket.emit('session-error', { error: error.message });
    }
  });
  
  socket.on('delete-session', async (data) => {
    try {
      const { sessionId } = data;
      await whatsappGateway.deleteSession(sessionId);
      socket.emit('session-deleted', { sessionId, success: true });
    } catch (error) {
      socket.emit('session-error', { error: error.message });
    }
  });
});

const PORT = process.env.WHATSAPP_PORT || 3000;

server.listen(PORT, () => {
  logger.info(`WhatsApp Gateway Server running on port ${PORT}`);
  logger.info(`Socket.IO server ready for connections`);
  logger.info(`Dashboard available at: http://localhost:8000`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Shutting down WhatsApp Gateway...');
  await whatsappGateway.cleanup();
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', async () => {
  logger.info('Shutting down WhatsApp Gateway...');
  await whatsappGateway.cleanup();
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});